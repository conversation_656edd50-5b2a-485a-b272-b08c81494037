import admin from 'firebase-admin';
import { env } from "$env/dynamic/private";

// 配置邮件链接设置
// 身份认证 rest api docs
// https://cloud.google.com/identity-platform/docs/use-rest-api?hl=zh-cn
// 邮箱链接认证请求回调地址
// 该页面会请求 https://www.googleapis.com/identitytoolkit/v3/relyingparty/getProjectConfig?key=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw
// https://ai-novel-983d1.firebaseapp.com/__/auth/action?apiKey=AIzaSyB3EAt_QmIvQLmnsgRcEklmKPXwbU05ymw&mode=signIn&oobCode=n2bfvW4wRiyUFHe8MmqturQkvj-rRodujU-wI4A0qwsAAAGYdf5sAw&continueUrl=http://localhost:3004/login/callback&lang=en
export function initializeFirebaseAdmin() {
    const serviceAccount = JSON.parse(
        env.GOOGLE_FIREBASE_ADMIN_SERVICE_ACCOUNT
    );

    if (!admin.apps || !admin.apps.length) {
        admin.initializeApp({
            credential: admin.credential.cert(serviceAccount), // Your service account JSON
        });
    }
}

export async function validActivationCode(code: string) {
    const db = admin.firestore();
    const collectionRef = db.collection("activation_codes");

    // 获取文档列表
    let query = collectionRef.limit(10);
    const snapshot = await query.get();

    const documents = snapshot.docs.map(doc => ({
        id: doc.id,
        data: doc.data(),
        createTime: doc.createTime?.toDate(),
        updateTime: doc.updateTime?.toDate()
    }));
    // doc.data.codes 是 map 结构
    const isValid = documents.some(doc => {
        if (doc.data.codes) {
            const map = new Map(Object.entries(doc.data.codes));
            return map.has(code) && map.get(code) === true;
        }
        return false;
    });

    return { isValid, snapshot };
}

export async function validUserToken(email: string, token: string) {
    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken.email ? decodedToken.email === email : false;
}

export async function saveActivationCode(email: string, activationCode: string, snapshot?: admin.firestore.QuerySnapshot<admin.firestore.DocumentData, admin.firestore.DocumentData>) {
    await admin.auth().setCustomUserClaims(email, { activationCode });
    // 从 firestore 中标记该激活码为 false (已使用)
    let newsnapshot;
    if (snapshot) {
        newsnapshot = snapshot;
    } else {
        const db = admin.firestore();
        const collectionRef = db.collection("activation_codes");
        newsnapshot = snapshot ?? (await collectionRef.where("codes", "array-contains", activationCode).get());
    }
    newsnapshot.forEach(async doc => {
        const codes = doc.data().codes;
        const index = codes.indexOf(activationCode);
        if (index > -1) {
            codes[index] = false;
            await doc.ref.update({ codes });
        }
    });
}