import { env } from "$env/dynamic/private";
import type { GeminiRequest } from "$lib/models/gemini.model";
import type { ChatCompletionCreateParamsNonStreaming } from "openai/resources.js";

export async function openai(signal: AbortSignal, requestBody: ChatCompletionCreateParamsNonStreaming) {
    const model = requestBody.model;
    let url = "";
    let token = "";
    if (model.includes("gemini")) {
        url = "https://generativelanguage.googleapis.com/v1beta/chat/completions";
        token = env.GEMINI_API_KEY;
    } else if (model.includes("deepseek")) {
        url = "https://api.deepseek.com/chat/completions";
        token = env.DEEPSEEK_API_KEY;
    }

    const resp = await fetch(url,
        {
            signal: signal,
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + token,
            },
            body: JSON.stringify(requestBody),
        }
    );
    const headers = new Headers(resp.headers);
    headers.delete('content-encoding');
    headers.delete('content-length');

    return new Response(resp.body, {
        status: resp.status,
        headers: headers,
    });
}

// deepseek 
export async function gemini(signal: AbortSignal, model: string, request: GeminiRequest) {
    const resp = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`,
        {
            signal: signal,
            method: "POST",
            headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
                "x-goog-api-key": env.GEMINI_API_KEY,
            },
            body: JSON.stringify(request),
        }
    );
    const headers = new Headers(resp.headers);
    headers.delete('content-encoding');
    headers.delete('content-length');

    return new Response(resp.body, {
        status: resp.status,
        headers: headers,
    });
}