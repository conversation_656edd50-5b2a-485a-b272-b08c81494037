import { browser } from "$app/environment";
import { goto } from "$app/navigation";
import { addToast } from "$lib/components/toast/toastStore";
import { userAuth, activationCode } from "$lib/stores/app.store";
import { get } from "svelte/store";
import { GoogleAuthProvider, signInWithPopup } from "firebase/auth";
import { fireapp } from "$lib/firebase/firebase";
import { env } from "$env/dynamic/public";
import { InputValidator, authRateLimiter, emailRateLimiter } from "$lib/utils/security";
import { createPersistedStore } from "./local.service";

// --- Constants for Firebase REST APIs ---
const API_BASE_URL = "/api/googleapis?rest=";
const SIGN_UP_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signUp";
const SIGN_IN_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword";
const SIGN_IN_EMAIL_LINK_URL = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithEmailLink";
const SEND_OOB_CODE_URL = "https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode";
const UPDATE_ACCOUNT_URL = "https://identitytoolkit.googleapis.com/v1/accounts:update";
const RESET_PASSWORD_URL = "https://identitytoolkit.googleapis.com/v1/accounts:resetPassword";
const REFRESH_TOKEN_URL = "https://securetoken.googleapis.com/v1/token";
const GET_ACCOUNT_INFO_URL = "https://identitytoolkit.googleapis.com/v1/accounts:lookup";

interface ProviderData {
  displayName: string | null;
  email: string | null;
  phoneNumber: string | null;
  photoURL: string | null;
  providerId: string;
  uid: string;
}

export interface AuthUser {
  uid: string;
  email: string;
  displayName?: string;
  emailVerified: boolean;
  photoURL?: string;
  phoneNumber?: string;
  createdAt?: string;
  lastLoginAt?: string;
  isAnonymous?: boolean;
  idToken: string;
  refreshToken: string;
  expiresIn?: string;
  localId?: string;
  registered?: boolean;
  activated?: boolean;
  providerData?: ProviderData[];
}

export interface AuthError {
  code: string;
  message: string;
}

export interface AuthResponse {
  success: boolean;
  user?: AuthUser;
  error?: AuthError;
  redirectUrl?: string;
}

class AuthService {
  private refreshTimer: NodeJS.Timeout | null = null;
  private readonly TOKEN_REFRESH_INTERVAL = 59 * 60 * 1000; // 59 minutes
  // email 临时存储
  private emailTemp = createPersistedStore<string>("emailTemp", "");

  constructor() {
    if (browser) {
      this.initializeAuth();
    }
  }

  private async initializeAuth() {
    if (this.isAuthenticated()) {
      await this.checkAndRefreshToken();
      const currentUser = get(userAuth);
      const userInfo = await this.getUserInfo();
      this._setUser({ ...currentUser, ...userInfo });
    }
  }

  // --- UNIFIED USER STATE MANAGEMENT ---

  private _setUser(firebaseUserData: any): AuthUser {
    const user: AuthUser = {
      uid: firebaseUserData.localId || firebaseUserData.uid,
      email: firebaseUserData.email,
      emailVerified: firebaseUserData.emailVerified || false,
      idToken: firebaseUserData.idToken,
      refreshToken: firebaseUserData.refreshToken,
      expiresIn: firebaseUserData.expiresIn,
      localId: firebaseUserData.localId,
      registered: firebaseUserData.registered || false,
      ...firebaseUserData,
    };

    userAuth.set(user);
    // this.checkAndRefreshToken();
    return user;
  }

  // --- AUTHENTICATION METHODS ---

  async signUp(email: string, password: string): Promise<AuthResponse> {
    try {
      // 输入验证
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      const passwordValidation = InputValidator.validatePassword(password);
      if (!passwordValidation.isValid) {
        return { success: false, error: { code: "WEAK_PASSWORD", message: passwordValidation.errors[0] || "密码强度不够" } };
      }

      // 频率限制检查
      const rateLimitKey = `signup_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `注册尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_UP_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) return { success: false, error: this.parseFirebaseError(data.error) };

      const user = this._setUser(data);
      this.emailTemp.set(cleanEmail);
      await this.sendEmailVerification(user.idToken);

      // 注册成功，重置频率限制
      authRateLimiter.reset(rateLimitKey);

      return this._checkVerificationAndActivation(data);
    } catch (error) {
      return this._handleNetworkError(error, "注册失败");
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    try {
      // 输入验证
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        return { success: false, error: { code: "INVALID_EMAIL", message: emailValidation.error || "邮箱格式无效" } };
      }

      if (!password || password.trim().length === 0) {
        return { success: false, error: { code: "MISSING_PASSWORD", message: "请输入密码" } };
      }

      // 频率限制检查
      const rateLimitKey = `signin_${email.trim().toLowerCase()}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        return {
          success: false,
          error: {
            code: "TOO_MANY_ATTEMPTS",
            message: `登录尝试过于频繁，请${remainingTime}分钟后再试`
          }
        };
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_PASSWORD_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: cleanEmail, password, returnSecureToken: true }),
      });

      const data = await response.json();
      if (!response.ok) {
        // 登录失败不重置频率限制，让其继续累积
        return { success: false, error: this.parseFirebaseError(data.error) };
      }

      // 登录成功，重置频率限制
      authRateLimiter.reset(rateLimitKey);

      // Get user info
      const userInfo = await this.getUserInfo(data.idToken);

      const user = this._setUser({ ...data, ...userInfo });
      return this._checkVerificationAndActivation(user);

    } catch (error) {
      return this._handleNetworkError(error, "登录失败");
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    try {
      const fire = fireapp();
      const provider = new GoogleAuthProvider();
      provider.addScope("profile");
      provider.addScope("email");
      provider.setCustomParameters({ prompt: "select_account" });

      const result = await signInWithPopup(fire.auth, provider);
      const credential = GoogleAuthProvider.credentialFromResult(result);

      if (!credential?.idToken) {
        throw new Error("Google登录失败，无法获取ID Token");
      }

      // Get user info
      const userInfo = await this.getUserInfo(credential.idToken);

      // Here you would typically send the idToken to your backend to verify it,
      // create a session, and return your own app's user object and session token.
      // For this refactoring, we'll simulate this by directly using the Firebase user object.
      const user = this._setUser({
        ...result.user,
        ...userInfo,
        idToken: await result.user.getIdToken(),
        refreshToken: result.user.refreshToken, // Note: refreshToken might not be available directly
      });

      addToast({ type: "success", message: "Google 登录成功" });
      return this._checkVerificationAndActivation(user);

    } catch (error: any) {
      addToast({ type: "error", message: error.message || "Google 登录失败" });
      return { success: false, error: this.parseFirebaseError(error) };
    }
  }

  // --- OOB (OUT-OF-BAND) CODE HANDLING ---

  async handleAuthCallback(mode: string, oobCode: string): Promise<AuthResponse> {
    switch (mode) {
      case "signIn":
        return await this.handleEmailLinkSignIn(oobCode);
      case "verifyEmail":
        return await this.handleEmailVerification(oobCode);
      case "resetPassword":
        // The UI will handle showing the form, this service just processes the final reset
        return { success: true, redirectUrl: `/auth/reset-password?oobCode=${encodeURIComponent(oobCode)}` };
      default:
        throw new Error("无效的操作链接");
    }
  }

  async handleEmailLinkSignIn(oobCode: string): Promise<AuthResponse> {
    try {
      let emailForSignIn = get(this.emailTemp);
      if (!emailForSignIn) {
        emailForSignIn = window.prompt("请输入您的邮箱地址以完成登录:") || "";
      }
      if (!emailForSignIn) throw new Error("需要邮箱地址才能完成登录");

      const response = await fetch(`${API_BASE_URL}${SIGN_IN_EMAIL_LINK_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: emailForSignIn.trim(), oobCode }),
      });

      const data = await response.json();
      if (!response.ok) throw data.error;

      // Get user info
      const userInfo = await this.getUserInfo(data.idToken);

      this.emailTemp.set("");
      const user = this._setUser({ ...data, ...userInfo });

      addToast({ type: "success", message: `邮件链接登录成功！欢迎 ${user.email}` });
      return this._checkVerificationAndActivation(user);
    } catch (error) {
      const errorMessage = this.parseFirebaseError(error).message;
      addToast({ type: "error", message: errorMessage });
      throw new Error("邮件链接登录失败: " + errorMessage);
    }
  }

  async handleEmailVerification(oobCode: string): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}${UPDATE_ACCOUNT_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oobCode }),
      });

      const data = await response.json();

      if (!response.ok) throw this.parseFirebaseError(data);

      // Refresh user state to get the latest emailVerified status
      const currentUser = get(userAuth);
      if (currentUser.idToken) {
        // You might need a way to get full user profile again
        // For now, let's assume the user is logged in and we can just update the flag locally
        const user = this._setUser({ ...currentUser, ...data })
        addToast({ type: "success", message: "邮箱验证成功！" });
        return this._checkVerificationAndActivation(user);
      } else {
        addToast({ type: "success", message: "邮箱验证成功！请重新登录" });
        return { success: true };
      }
    } catch (error) {
      addToast({ type: "error", message: "邮箱验证失败，请重试" });
      throw this._handleNetworkError(error, "邮箱验证失败, 请重试")
    }
  }

  async handlePasswordReset(oobCode: string, newPassword: string): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}${RESET_PASSWORD_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ oobCode, newPassword }),
      });

      if (!response.ok) throw (await response.json()).error;

      return true;
    } catch (error) {
      return false;
    }
  }

  // --- OTHER PUBLIC METHODS ---

  verifyEmail(email: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return false;
    }
    return true;
  }

  async sendOobcode(requestType: "EMAIL_SIGNIN" | "PASSWORD_RESET" | "VERIFY_EMAIL" | "VERIFY_AND_CHANGE_EMAIL", data: any) {
    const continueUrl = `${window.location.origin}${env.PUBLIC_GOOGLE_FIREBASE_AUTH_CONTINUE_URL ?? ""}`
    return fetch(`${API_BASE_URL}${SEND_OOB_CODE_URL}`, {
      method: "POST",
      headers: { "Content-Type": "application/json", "X-Firebase-Locale": navigator.language || "en" },
      body: JSON.stringify({ requestType, continueUrl, ...data }),
    });
  }

  async sendLoginLink(email: string): Promise<void> {
    if (!email.trim()) return;

    // 输入验证
    const emailValidation = InputValidator.validateEmail(email);
    if (!emailValidation.isValid) {
      addToast({ type: "error", message: emailValidation.error || "请输入有效的邮箱地址" });
      return;
    }

    // 邮件发送频率限制
    const rateLimitKey = `email_login_${email.trim().toLowerCase()}`;
    if (!emailRateLimiter.isAllowed(rateLimitKey)) {
      const remainingTime = Math.ceil(emailRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
      addToast({
        type: "error",
        message: `邮件发送过于频繁，请${remainingTime}分钟后再试`
      });
      return;
    }

    try {
      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await this.sendOobcode("EMAIL_SIGNIN", {
        email: cleanEmail,
        clientType: "CLIENT_TYPE_WEB",
      });

      if (response.ok) {
        this.emailTemp.set(cleanEmail);
        addToast({ type: "info", message: "登录链接已发送到您的邮箱" });
      } else {
        const data = await response.json();
        addToast({ type: "error", message: this.parseFirebaseError(data.error).message });
      }
    } catch (error) {
      this._handleNetworkError(error, "发送邮件链接失败");
    }
  }

  async sendEmailVerification(idToken?: string): Promise<boolean> {
    try {
      const token = idToken || get(userAuth)?.idToken;
      if (!token) throw new Error("未找到用户token");

      const response = await this.sendOobcode("VERIFY_EMAIL", {
        idToken: token,
      });

      if (response.ok) {
        addToast({ type: "success", message: "验证邮件已发送，请检查您的邮箱" });
        return true;
      }
      throw new Error("发送验证邮件失败");
    } catch (error) {
      addToast({ type: "error", message: "发送验证邮件失败，请稍后重试" });
      return false;
    }
  }

  async sendPasswordResetEmail(email: string): Promise<boolean> {
    try {
      // 输入验证
      const emailValidation = InputValidator.validateEmail(email);
      if (!emailValidation.isValid) {
        addToast({ type: "error", message: emailValidation.error || "请输入有效的邮箱地址" });
        return false;
      }

      // 邮件发送频率限制
      const rateLimitKey = `email_reset_${email.trim().toLowerCase()}`;
      if (!emailRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(emailRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        addToast({
          type: "error",
          message: `邮件发送过于频繁，请${remainingTime}分钟后再试`
        });
        return false;
      }

      const cleanEmail = InputValidator.sanitizeString(email.trim(), 254);

      const response = await this.sendOobcode("PASSWORD_RESET", {
        email: cleanEmail,
      });

      if (response.ok) {
        addToast({ type: "success", message: "密码重置邮件已发送，请检查您的邮箱" });
        return true;
      }
      const data = await response.json();
      throw new Error(data.error?.message || "发送重置邮件失败");
    } catch (error) {
      addToast({ type: "error", message: "发送重置邮件失败，请稍后重试" });
      return false;
    }
  }

  async refreshToken(): Promise<boolean> {
    const currentUser = get(userAuth);
    if (!currentUser?.refreshToken) {
      console.warn("没有refresh token，无法刷新");
      return false;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${REFRESH_TOKEN_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ grant_type: "refresh_token", refresh_token: currentUser.refreshToken }),
      });

      const data = await response.json();
      if (!response.ok) {
        this.signOut();
        return false;
      }

      const updatedUser = { ...currentUser, idToken: data.id_token, refreshToken: data.refresh_token, expiresIn: data.expires_in };
      userAuth.set(updatedUser);
      return true;
    } catch (error) {
      this.signOut();
      return false;
    }
  }

  // 检查并刷新 JWT
  async checkAndRefreshToken(): Promise<boolean> {
    const currentUser = get(userAuth);
    if (!currentUser?.idToken) return false;
    // 判断 idtoken 是否过期
    const expiresAt = new Date(currentUser.lastLoginAt ? Number(currentUser.lastLoginAt) : Date.now());
    expiresAt.setSeconds(expiresAt.getSeconds() + Number(currentUser.expiresIn));
    if (expiresAt > new Date()) return true;
    // This logic can be improved to check actual token expiry
    return await this.refreshToken();
  }

  // 定时刷新 JWT
  private setupTokenRefresh() {
    if (this.refreshTimer) clearInterval(this.refreshTimer);
    this.refreshTimer = setInterval(() => this.refreshToken(), this.TOKEN_REFRESH_INTERVAL);
  }

  async getUserInfo(idToken?: string) {
    const token = idToken || this.getCurrentUser()?.idToken;
    if (!token) {
      console.warn("用户未登录");
      return false;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${GET_ACCOUNT_INFO_URL}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ idToken: token }),
      });

      const data = await response.json();
      if (!response.ok) {
        this.signOut();
        return false;
      }

      if (!data.users || 0 == data.users.length) {
        this.signOut();
        return false;
      }

      const user = data.users[0];
      // console.log('getUserInfo: ', user);

      return user;
    } catch (error) {
      this.signOut();
      throw error;
    }
  }

  async verifyActivationCode(code: string): Promise<boolean> {
    try {
      const currentUser = get(userAuth);
      if (!currentUser?.email || !currentUser?.idToken) throw new Error("用户未登录");

      // 验证激活码格式
      const codeValidation = InputValidator.validateActivationCode(code);
      if (!codeValidation.isValid) {
        addToast({ type: "error", message: codeValidation.error || "激活码格式无效" });
        return false;
      }

      // 频率限制检查
      const rateLimitKey = `activate_${currentUser.email}`;
      if (!authRateLimiter.isAllowed(rateLimitKey)) {
        const remainingTime = Math.ceil(authRateLimiter.getRemainingTime(rateLimitKey) / 1000 / 60);
        addToast({
          type: "error",
          message: `激活尝试过于频繁，请${remainingTime}分钟后再试`
        });
        return false;
      }

      const cleanCode = InputValidator.sanitizeString(code.replace(/[^A-Za-z0-9]/g, '').toUpperCase(), 20);

      const response = await fetch("/api/user/activate", {
        method: "POST",
        headers: { "Content-Type": "application/json", "Authorization": `Bearer ${currentUser.idToken}` },
        body: JSON.stringify({ email: currentUser.email, activationCode: cleanCode }),
      });

      if (response.ok) {
        activationCode.set(cleanCode);
        authRateLimiter.reset(rateLimitKey); // 激活成功，重置频率限制
        addToast({ type: "success", message: "激活成功！欢迎使用蘑菇AI小说平台" });
        return true;
      }
      const data = await response.json();
      throw new Error(data.error || "激活码验证失败");
    } catch (error: any) {
      addToast({ type: "error", message: error.message || "激活码验证失败" });
      return false;
    }
  }

  async signOut(implicit: boolean = true) {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
    userAuth.set({} as AuthUser);
    this.emailTemp.set("");
    activationCode.set("");
    if (browser) goto("/login");
    if (!implicit) addToast({ type: "info", message: "已退出登录" });
  }

  // --- GETTERS ---

  getCurrentUser = () => get(userAuth) && get(userAuth).idToken ? get(userAuth) : null;
  isAuthenticated = () => !!this.getCurrentUser()?.idToken;
  isEmailVerified = () => !!this.getCurrentUser()?.emailVerified;
  isActivated = () => !!this.getCurrentUser()?.activated;

  // --- PRIVATE HELPERS ---

  private _checkVerificationAndActivation(user: AuthUser): AuthResponse {
    if (!user.emailVerified) {
      return { success: true, user, redirectUrl: "/auth/verify-email" };
    }
    if (!user.activated) {
      return { success: true, user, redirectUrl: "/auth/activate" };
    }
    return { success: true, user };
  }

  private _handleNetworkError(error: any, defaultMessage: string): AuthResponse {
    console.error(`${defaultMessage}:`, error);
    return {
      success: false,
      error: { code: "auth/network-error", message: "网络错误，请稍后重试" },
    };
  }

  private parseFirebaseError(error: any): AuthError {
    const errorCode = error?.message || error?.code || "UNKNOWN_ERROR";
    const errorMessages: Record<string, string> = {
      "EMAIL_EXISTS": "该邮箱已被注册",
      "OPERATION_NOT_ALLOWED": "该操作不被允许",
      "TOO_MANY_ATTEMPTS_TRY_LATER": "尝试次数过多，请稍后再试",
      "EMAIL_NOT_FOUND": "邮箱地址不存在",
      "INVALID_PASSWORD": "密码错误",
      "USER_DISABLED": "用户账户已被禁用",
      "INVALID_EMAIL": "邮箱地址格式无效",
      "WEAK_PASSWORD": "密码强度不够，至少需要6位字符",
      "MISSING_PASSWORD": "请输入密码",
      "INVALID_LOGIN_CREDENTIALS": "邮箱或密码错误",
      "INVALID_ACTION_CODE": "链接无效或已过期，请重试",
      "popup-closed-by-user": "登录窗口被用户关闭",
      "popup-blocked": "登录弹窗被浏览器阻止，请允许弹窗后重试",
    };
    return {
      code: errorCode,
      message: errorMessages[errorCode] || "未知错误，请稍后重试: \n" + JSON.stringify(error),
    };
  }
}

export const authService = new AuthService();
