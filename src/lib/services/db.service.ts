import { writable } from "svelte/store";
import type { Database, DataRecord } from "../indexeddb/db";

export type Page<T> = {
    index: number;
    pageSize: number;
    pageCount: number;
    items: T[];
    total: number;
}

/**
 * 创建一个与 IndexedDB 表同步的 Svelte store 服务。
 *
 * @template T - 存储中记录的数据类型，必须扩展自 DataRecord。
 * @param {Database} db - `Database` 类的实例。
 * @param {string} storeName - 要操作的 IndexedDB 对象存储的名称。
 * @param {Partial<Page<T>>} [defaultPageOptions] - store 的初始分页设置。
 * @returns 一个包含 Svelte store、加载状态、错误信息以及所有数据库操作方法的对象。
 */
export const createDatabaseStore = <T extends DataRecord>(
    db: Database,
    storeName: string,
    defaultPageOptions: Partial<Page<T>> = {}
) => {
    const defaultPage: Page<T> = {
        index: 1,
        pageSize: 20,
        pageCount: 0,
        items: [],
        total: 0,
        ...defaultPageOptions
    };

    const { set, subscribe, update } = writable<Page<T>>(defaultPage);

    const isLoading = writable(false);
    const error = writable<Error | null>(null);

    let currentPage: Page<T> = defaultPage;
    subscribe((value) => {
        currentPage = value;
    })

    const handleError = (err: any) => {
        console.error(`Database error in store ${storeName}:`, err);
        error.set(err instanceof Error ? err : new Error(String(err)));
    };

    const execute = async <R>(action: () => Promise<R>): Promise<R | undefined> => {
        isLoading.set(true);
        error.set(null);
        try {
            return await action();
        } catch (err) {
            handleError(err);
            return undefined;
        } finally {
            isLoading.set(false);
        }
    };

    const sort = (data: T[]) => {
        return data.sort((a, b) => new Date(b.timestamp!).getTime() - new Date(a.timestamp!).getTime());
    };

    const load = (page: number, pageSize: number) => execute(async () => {
        await db.open();
        const total = await db.count(storeName);
        const pageCount = Math.ceil(total / pageSize) || 1;
        const items = await db.page(storeName, page, pageSize) as T[];

        const currentPage = {
            index: page,
            pageSize,
            pageCount,
            items: sort(items),
            total,
        };
        set(currentPage);
        return currentPage;
    });

    // 初始化时自动加载
    load(currentPage.index, currentPage.pageSize);

    return {
        // Svelte store
        subscribe,
        // 状态
        isLoading,
        error,
        // 数据库实例
        db,

        // 操作
        load: () => load(defaultPage.index, currentPage.pageSize),

        add: (data: T) => execute(async () => {
            const key = await db.add(storeName, data);
            const item = await db.get(storeName, key);
            currentPage.items.unshift(item as T);
            currentPage.total += 1;
            set(currentPage);
            return item;
        }),

        upsert: (data: T) => execute(async () => {
            await db.update(storeName, data);
            update(p => ({
                ...p,
                items: p.items.map(item => item.id === data.id ? data : item)
            }));
            return data;
        }),

        delete: (key: IDBValidKey) => execute(async () => {
            await db.delete(storeName, key);
            if (currentPage.items.length === 1 && currentPage.index > 1) {
                await load(currentPage.index - 1, currentPage.pageSize);
            } else {
                await load(currentPage.index, currentPage.pageSize);
            }
        }),

        clear: () => execute(async () => {
            await db.clear(storeName);
            set({ ...defaultPage, total: 0, pageCount: 0, items: [] });
        }),

        search: (query: string, sortConfig?: { name: string, dir: "asc" | "desc" }) => execute(async () => {
            const results = await db.search(storeName, query, sortConfig) as T[];
            set({
                ...currentPage,
                index: 1,
                pageCount: 1,
                items: results,
                total: results.length,
            });
        }),

        all: () => execute(async () => {
            const results = await db.all(storeName) as T[];
            set({
                ...currentPage,
                index: 1,
                pageCount: 1,
                items: sort(results),
                total: results.length,
            });
        }),

        get: (key: IDBValidKey) => execute(async () => {
            const finditem = currentPage.items.find(item => item.id === key);
            if (finditem) return finditem;
            return db.get(storeName, key) as Promise<T | undefined>;
        }),

        page: (page: number, pageSize: number) => load(page, pageSize),

        next: () => load(currentPage.index + 1, currentPage.pageSize),
        prev: () => load(currentPage.index - 1, currentPage.pageSize),
    };
}