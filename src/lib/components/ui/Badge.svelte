<script lang="ts">
  interface Props {
    variant?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | "info";
    size?: "sm" | "md" | "lg";
    class?: string;
    children?: any;
  }

  let {
    variant = "default",
    size = "md",
    class: className = "",
    children
  }: Props = $props();

  // 基础样式
  const baseClasses = "inline-flex items-center font-medium rounded-full";

  // 变体样式
  const variantClasses = {
    default: "bg-gray-100 text-gray-800",
    primary: "bg-blue-100 text-blue-800",
    secondary: "bg-purple-100 text-purple-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    danger: "bg-red-100 text-red-800",
    info: "bg-cyan-100 text-cyan-800"
  };

  // 尺寸样式
  const sizeClasses = {
    sm: "px-2 py-0.5 text-xs",
    md: "px-2.5 py-1 text-sm",
    lg: "px-3 py-1.5 text-base"
  };

  // 组合样式
  const computedClasses = $derived([
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  ].filter(Boolean).join(" "));
</script>

<span class={computedClasses}>
  {@render children?.()}
</span>
