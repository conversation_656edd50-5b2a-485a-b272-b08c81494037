<script lang="ts">
    import { Plus } from "lucide-svelte";
    import type { HTMLAttributes } from "svelte/elements";
    import Button from "./Button.svelte";

    interface TabItem {
        id: string;
        label: string;
        icon?: string;
        disabled?: boolean;
        badge?: string | number;
    }

    interface Props extends HTMLAttributes<HTMLDivElement> {
        tabs: TabItem[];
        activeTab?: string;
        variant?: "default" | "pills" | "underline" | "cards";
        size?: "sm" | "md" | "lg";
        fullWidth?: boolean;
        vertical?: boolean;
        onTabChange?: (tabId: string) => void;
        children?: any;
    }

    let {
        tabs,
        activeTab = $bindable(tabs[0]?.id || ""),
        variant = "default",
        size = "md",
        fullWidth = false,
        vertical = false,
        onTabChange,
        class: className = "",
        children,
        ...restProps
    }: Props = $props();

    // 基础样式
    const baseClasses = "flex";

    // 容器样式
    const containerClasses = $derived(
        [
            baseClasses,
            vertical ? "flex-col" : "flex-row",
            fullWidth ? "w-full" : "",
            className,
        ]
            .filter(Boolean)
            .join(" "),
    );

    // Tab列表样式
    const tabListClasses = $derived(
        [
            "flex flex-row overflow-x-auto",
            variant === "cards" ? "bg-gray-100 p-1 rounded-lg" : "",
            variant === "underline" ? "border-b border-gray-200" : "",
            fullWidth ? "w-full" : "",
        ]
            .filter(Boolean)
            .join(" "),
    );

    // 尺寸样式
    const sizeClasses = {
        sm: "px-3 py-1.5 text-sm",
        md: "px-4 py-2 text-base",
        lg: "px-6 py-3 text-lg",
    };

    // 变体样式
    const getTabClasses = (tab: TabItem, isActive: boolean) => {
        const baseTabClasses = [
            "space-x-2 relative inline-flex items-center justify-center font-medium transition-all duration-200",
            sizeClasses[size],
            fullWidth && !vertical ? "flex-1" : "",
            tab.disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
        ];

        switch (variant) {
            case "pills":
                return [
                    ...baseTabClasses,
                    "rounded-full",
                    isActive
                        ? "bg-blue-600 text-white shadow-md"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100",
                ]
                    .filter(Boolean)
                    .join(" ");

            case "underline":
                return [
                    ...baseTabClasses,
                    "border-b-2 rounded-none",
                    isActive
                        ? "border-blue-600 text-blue-600"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300",
                ]
                    .filter(Boolean)
                    .join(" ");

            case "cards":
                return [
                    ...baseTabClasses,
                    "rounded-md",
                    isActive
                        ? "bg-white text-gray-900 shadow-sm"
                        : "text-gray-600 hover:text-gray-900 hover:bg-white/50",
                ]
                    .filter(Boolean)
                    .join(" ");

            default:
                return [
                    ...baseTabClasses,
                    "rounded-lg",
                    isActive
                        ? "bg-blue-50 text-blue-700 border border-blue-200"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50 border border-transparent",
                ]
                    .filter(Boolean)
                    .join(" ");
        }
    };

    // 处理Tab切换
    const handleTabClick = (tabId: string, disabled?: boolean) => {
        if (disabled) return;
        activeTab = tabId;
        onTabChange?.(tabId);
    };

    // 键盘导航
    const handleKeydown = (
        event: KeyboardEvent,
        tabId: string,
        disabled?: boolean,
    ) => {
        if (event.key === "Enter" || event.key === " ") {
            event.preventDefault();
            handleTabClick(tabId, disabled);
        }
    };
</script>

<div class={containerClasses} {...restProps}>
    <!-- Tab 列表 -->
    <div class={tabListClasses} role="tablist">
        {#each tabs as tab (tab.id)}
            <button
                type="button"
                role="tab"
                tabindex={activeTab === tab.id ? 0 : -1}
                aria-selected={activeTab === tab.id}
                aria-controls="tabpanel-{tab.id}"
                class={getTabClasses(tab, activeTab === tab.id)}
                disabled={tab.disabled}
                onclick={() => handleTabClick(tab.id, tab.disabled)}
                onkeydown={(e) => handleKeydown(e, tab.id, tab.disabled)}
            >
                <!-- 图标 -->
                {#if tab.icon}
                    <span>
                        {@html tab.icon}
                    </span>
                {/if}

                <!-- 标签文本 -->
                <span class="hidden sm:inline-flex shrink-0">{tab.label}</span>

                <!-- 徽章 -->
                {#if tab.badge}
                    <span
                        class="inline-flex items-center justify-center text-xs leading-none text-white bg-red-600 rounded-full"
                        style="padding: 2px 4px;"
                    >
                        {tab.badge}
                    </span>
                {/if}
            </button>
        {/each}
        <div class="flex-1"></div>
        <Button variant="ghost" size="sm"><Plus></Plus></Button>
    </div>

    <!-- Tab 内容区域 -->
    <div class="mt-2">
        {#each tabs as tab (tab.id)}
            <div
                id="tabpanel-{tab.id}"
                role="tabpanel"
                aria-labelledby="tab-{tab.id}"
                class={activeTab === tab.id ? "block" : "hidden"}
            >
                {@render children?.(tab)}
            </div>
        {/each}
    </div>
</div>

<style>
    /* 确保焦点样式在所有变体中都可见 */
    button:focus-visible {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }
</style>
