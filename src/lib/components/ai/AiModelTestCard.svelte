<script lang="ts">
    import { type GeminiConfig } from "$lib/models/gemini.model";
    import AutoHeightTextarea from "../ui/AutoHeightTextarea.svelte";
    import Button from "../ui/Button.svelte";
    import Card from "../ui/Card.svelte";
    import Loading from "../ui/Loading.svelte";
    import { createOpenaiClient } from "$lib/services/openai.service";
    import { appModelsTestChats } from "$lib/stores/app.store";
    import type { CompletionUsage } from "openai/resources";
    import Badge from "../ui/Badge.svelte";

    interface Props {
        id: string;
        prompt: string;
        text: string;
        open?: boolean;
        onselectprompt?: (prompt: string) => void;
        onselecttext?: (text: string) => void;
    }

    let {
        id,
        prompt,
        text,
        open = false,
        onselectprompt,
        onselecttext,
    }: Props = $props();

    let srcprompt = $state("");
    let srctext = $state("");
    let thought = $state("");
    let suggestion = $state("");
    let isSuggesting = $state(false);
    let error: any = $state();

    let model: string = $state("gemini-2.5-flash");
    let config: GeminiConfig = $state({
        maxOutputTokens: 600,
        thinkingConfig: {},
    });
    // let includeThoughts = $state(false);
    // let thinkingBudget: number | undefined = $state(undefined);

    // 耗时
    let time = $state(0);
    let time1 = $state(0);
    let usage: CompletionUsage | undefined = $state();

    let controller = new AbortController();

    let successed = $derived(
        suggestion != null &&
            suggestion != undefined &&
            suggestion.trim() !== "" &&
            suggestion.trim() !== text,
    );

    let usagePromptTokens = $derived(
        (usage?.prompt_tokens ?? 0) +
            (usage?.prompt_tokens_details?.cached_tokens
                ? `(${usage?.prompt_tokens_details?.cached_tokens})`
                : ""),
    );

    let usageCompletionTokens = $derived(
        (usage?.completion_tokens ?? 0) +
            (config.thinkingConfig.includeThoughts
                ? usage?.completion_tokens_details?.reasoning_tokens
                    ? `(${usage?.completion_tokens_details?.reasoning_tokens})`
                    : `(${(usage?.total_tokens || 0) - (usage?.prompt_tokens ?? 0) - (usage?.completion_tokens ?? 0)})`
                : ""),
    );

    $effect(() => {
        appModelsTestChats.get(id).then((result) => {
            if (!result) return;
            model = result.model;
            config = result.config;
            srcprompt = result.srcprompt;
            srctext = result.srctext;
            thought = result.thought;
            suggestion = result.suggestion;
            usage = result.usage;
            time = result.time;
            time1 = result.time1;
        });
    });

    async function startTest() {
        controller.abort();

        if (!text || text.trim().length < 3) return;

        // config.thinkingConfig = { includeThoughts, thinkingBudget };

        console.log(text);
        try {
            controller = new AbortController();
            isSuggesting = true;
            error = null;

            const date1 = new Date();

            const openai = createOpenaiClient();
            const response = await openai.chat.completions.create(
                {
                    model:
                        "deepseek-chat" == model &&
                        config.thinkingConfig.includeThoughts
                            ? "deepseek-reasoner"
                            : model,
                    messages: [
                        {
                            role: "system",
                            content: prompt,
                        },
                        { role: "user", content: text },
                    ],
                    temperature: config.temperature,
                    max_tokens: config.maxOutputTokens,
                    top_p: config.topP,
                    // @ts-expect-error Gemini 思考模型
                    extra_body: {
                        google: {
                            thinking_config: {
                                thinking_budget:
                                    config.thinkingConfig.thinkingBudget,
                                include_thoughts:
                                    config.thinkingConfig.includeThoughts,
                            },
                        },
                    },
                },
                { signal: controller.signal },
            );

            const date2 = new Date();
            time = date2.getTime() - date1.getTime();

            const content = response.choices[0].message.content || "";

            const regex = /<thought>([\s\S]*?)<\/thought>/;
            if (config.thinkingConfig.includeThoughts) {
                // @ts-expect-error deepseek 深度思考内容
                const reasoning = response.choices[0].message.reasoning_content;
                // <thought>**The Continuation. I think this is \"good\".\n</thought>如雨点般密集
                const thoughts = content.match(regex);
                thought = thoughts ? thoughts[1] : reasoning;
            }
            suggestion = content.replace(regex, "").replace(text, "");
            srcprompt = prompt;
            srctext = text;
            usage = response.usage;
            time1 = date2.getTime() - response.created * 1000;
            if (successed) {
                appModelsTestChats
                    .add({
                        id,
                        model,
                        config: $state.snapshot(config),
                        srcprompt,
                        srctext,
                        thought,
                        suggestion,
                        usage: $state.snapshot(usage),
                        time,
                        time1,
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            }
        } catch (err: any) {
            error = err;
        } finally {
            isSuggesting = false;
        }
    }
</script>

<Card>
    <div {id} class="flex flex-wrap item-center justify-between gap-1">
        <!-- 设置 gemini config -->
        <select
            bind:value={model}
            class="border rounded-sm"
            disabled={successed}
        >
            <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
            <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
            <option value="gemini-2.5-flash-preview-05-20"
                >Gemini 2.5 Flash Preview</option
            >
            <option value="gemini-2.5-flash-lite">Gemini 2.5 Flash-Lite</option>
            <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
            <option value="gemini-2.0-flash-001">Gemini 2.0 Flash 稳定</option>
            <option value="gemini-2.0-flash-exp"
                >Gemini 2.0 Flash 实验性
            </option>
            <option value="gemini-2.0-flash-lite">Gemini 2.0 Flash-Lite</option>
            <option value="gemini-2.0-flash-lite-001"
                >Gemini 2.0 Flash-Lite 稳定</option
            >
            <option value="gemini-2.0-flash-lite-preview"
                >Gemini 2.0 Flash-Lite Preview</option
            >
            <option value="deepseek-chat">DeepSeek</option>
            <!-- <option value="deepseek-reasoner">Deepseek Reasoner</option> -->
        </select>
        <Button class="h-8 mx-1" disabled={successed} onclick={startTest}
            >⚡️</Button
        >
        <input
            type="number"
            bind:value={config.maxOutputTokens}
            min="0"
            max="10000"
            step="100"
            placeholder="maxOutputTokens"
            disabled={successed}
        />
        <input
            type="number"
            bind:value={config.temperature}
            min="0"
            max="1"
            step="0.1"
            placeholder="temperature"
            disabled={successed}
        />
        <input
            type="number"
            bind:value={config.topP}
            min="0"
            max="1"
            step="0.1"
            placeholder="topP"
            disabled={successed}
        />
        <!-- thinking -->
        <input
            type="number"
            bind:value={config.thinkingConfig.thinkingBudget}
            min="-1"
            step="1"
            placeholder="thinkBudget"
            disabled={successed}
        />
        <input
            id="includeThoughts{id}"
            type="checkbox"
            bind:checked={config.thinkingConfig.includeThoughts}
            disabled={successed}
        />
        <label for="includeThoughts{id}" class="hidden sm:inline-flex"
            >思考</label
        >
    </div>
    <div class="mt-4">
        {#if isSuggesting}
            <Loading></Loading>
        {:else if error}
            <div class="text-red-500">{error.message}</div>
        {:else}
            {#if successed}
                <details>
                    <summary>Prompt </summary>
                    <AutoHeightTextarea
                        flex="bg-gray-100"
                        class="p-2"
                        value={srcprompt}
                        readonly={true}
                        disabled={true}
                    ></AutoHeightTextarea>
                    <div class="my-2 flex flex-row justify-end">
                        <Button
                            class="h-8 mx-1"
                            variant="outline"
                            onclick={() => {
                                onselectprompt?.(srcprompt);
                            }}
                            >应用 Prompt
                        </Button>
                        <Button
                            class="h-8 mx-1"
                            variant="outline"
                            onclick={() => {
                                onselecttext?.(srctext);
                            }}
                            >应用原文
                        </Button>
                    </div>
                </details>
            {/if}
            {#if config.thinkingConfig.includeThoughts}
                <details>
                    <summary>思考过程</summary>
                    <AutoHeightTextarea
                        flex="bg-gray-100"
                        class="p-2"
                        value={thought}
                        readonly={true}
                        disabled={true}
                    ></AutoHeightTextarea>
                </details>
            {/if}
            {#if successed}
                <div class="my-2 flex flex-row gap-1 item-center">
                    <span class="flex-1"></span>
                    <Button
                        style="display: {successed ? 'inline-flex' : 'none'}"
                        class="h-8 mx-1"
                        variant="outline"
                        onclick={() => {
                            onselecttext?.(srctext + suggestion);
                        }}
                        >应用全文
                    </Button>
                    <Button
                        class="h-8 mx-1"
                        onclick={() => {
                            open = !open;
                        }}
                    >
                        {open ? "😄" : "😝"}
                    </Button>
                </div>
            {/if}
            {#if open}
                <AutoHeightTextarea
                    value={`⚡️ ${(time / 1000).toFixed(2)}s ⚡️ ${srctext}${suggestion}`}
                    readonly={true}
                    disabled={true}
                ></AutoHeightTextarea>
            {/if}
            {#if successed}
                <div class="my-2 flex flex-wrap gap-1 item-center">
                    <Badge variant="info"
                        >⚡️ {(time1 / 1000).toFixed(2)}s ⚡️</Badge
                    >
                    <Badge variant="secondary">{usagePromptTokens}</Badge>
                    <Badge variant="success">{usageCompletionTokens}</Badge>
                    <Badge variant="primary">{usage?.total_tokens || 0}</Badge>
                </div>
            {/if}
        {/if}
    </div>
</Card>

<style>
    /* input 样式, 现代化 */
    input {
        width: 5rem;
        flex: 1;
        padding: 0;
    }
    input[type="checkbox"] {
        width: 2rem;
        padding: 0;
    }
</style>
