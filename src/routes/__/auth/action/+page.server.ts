
// 区分开发环境和生产环境的路由回调分发

import { redirect } from "@sveltejs/kit";

export async function load({ url }: { url: URL }) {
    const redirectUrl = "/__/auth/action";
    const continueUrl = url.searchParams.get("continueUrl");
    // 开发测试时使用的回调策略
    // 此地址应由 Google Firebase 授权服务调用, 由我们负责解析,
    // 但在开发时, 由于无法干预 firebase 的回调策略(比如开发时 firebase 回调开发地址等等),
    // 因此需要我们进行一次重定向
    if (continueUrl?.includes(redirectUrl)) {
        const params = url.searchParams;
        // 去除 continueUrl 参数，避免无限循环
        params.delete("continueUrl");
        throw redirect(302, redirectUrl + "?" + params.toString());
    }
}
