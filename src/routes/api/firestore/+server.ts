import { type Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { initializeFirebaseAdmin } from "$lib/firebase/firebase-admin";
import admin from 'firebase-admin';

export const GET: RequestHandler = async ({ request, url }) => {
    try {
        initializeFirebaseAdmin();

        const collectionPath: string = url.searchParams.get("collection") || "activation_codes";
        const db = admin.firestore();
        const collectionRef = db.collection(collectionPath);

        // 获取文档列表
        let query = collectionRef.limit(10);
        const snapshot = await query.get();

        const documents = snapshot.docs.map(doc => ({
            id: doc.id,
            data: doc.data(),
            createTime: doc.createTime?.toDate(),
            updateTime: doc.updateTime?.toDate()
        }));

        return new Response(JSON.stringify({
            success: true,
            data: {
                documents,
                count: documents.length,
                lastDocumentId: documents.length > 0 ? documents[documents.length - 1].id : null
            }
        }), {
            status: 200,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        }
        return new Response(JSON.stringify({
            success: false,
            error: `Generate error: ${err}`
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}