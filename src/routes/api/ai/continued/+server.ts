import { env } from "$env/dynamic/private";
import { error, type RequestH<PERSON><PERSON> } from "@sveltejs/kit";
import { proxy } from "$lib/utils/proxy"
import { initializeFirebaseAdmin, validActivationCode } from "$lib/firebase/firebase-admin";
import { openai } from "$lib/services/server.service";

export const POST: RequestHandler = async ({ request, url }) => {
    const time1 = new Date();

    console.log(time1, 0);

    const requestBody = await request.json();

    const activationCode = request.headers.get("Authorization")?.replace("Bearer ", "").trim();
    console.log('activationCode', activationCode);

    if (!activationCode || activationCode === "") {
        return error(401, "Unauthorized");
    }

    console.log(new Date(), new Date().getTime() - time1.getTime());

    let controller = new AbortController();

    // 在开发环境中使用代理请求
    const proxyResp = await proxy.json.post(url.pathname + url.search, {
        body: JSON.stringify(requestBody),
        headers: {
            Authorization: "Bearer " + activationCode,
        },
    });
    if (proxyResp) {
        console.log(new Date(), new Date().getTime() - time1.getTime());

        return proxyResp;
    }

    console.log('request', url.pathname + url.search);

    if (!env.GEMINI_API_KEY) {
        return new Response(JSON.stringify({
            success: false,
            error: "GEMINI_API_KEY not set"
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    // 获取 Fetch API 的 AbortSignal
    const signal = request.signal;

    // 监听客户端中断请求
    signal.addEventListener('abort', () => {
        console.log('Client aborted the request.');
        controller.abort(); // 如果你有其他异步任务，可以中止
    });

    try {
        initializeFirebaseAdmin();

        const isValid = validActivationCode(activationCode);

        console.log(new Date(), new Date().getTime() - time1.getTime());

        if (!isValid) {
            return error(401, "Invalid activation code");
        }

        const resp = openai(controller.signal, requestBody);

        console.log(new Date(), new Date().getTime() - time1.getTime());

        return resp;
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        }
        return new Response(JSON.stringify({
            success: false,
            error: `Generate error: ${err}`
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}