import { env } from "$env/dynamic/private";
import { error, type Request<PERSON><PERSON><PERSON> } from "@sveltejs/kit";
import { proxy } from "$lib/utils/proxy"
import type { GeminiRequest } from "$lib/models/gemini.model";
import { initializeFirebaseAdmin } from "$lib/firebase/firebase-admin";
import admin from 'firebase-admin';

export const POST: RequestHandler = async ({ request, url }) => {
    const requestBody: GeminiRequest = await request.json();

    const activationCode = request.headers.get("Authorization")?.replace("Bearer ", "").trim();
    console.log('activationCode', activationCode);

    if (!activationCode || activationCode === "") {
        return error(401, "Unauthorized");
    }

    // 在开发环境中使用代理请求
    const proxyResp = await proxy.json.post(url.pathname + url.search, {
        body: JSON.stringify(requestBody),
        headers: {
            Authorization: "Bearer " + activationCode,
        },
    });
    if (proxyResp) return proxyResp;

    console.log('request', url.pathname + url.search);

    if (!env.GEMINI_API_KEY) {
        return new Response(JSON.stringify({
            success: false,
            error: "GEMINI_API_KEY not set"
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }

    let controller = new AbortController();
    // 获取 Fetch API 的 AbortSignal
    const signal = request.signal;

    // 监听客户端中断请求
    signal.addEventListener('abort', () => {
        console.log('Client aborted the request.');
        controller.abort(); // 如果你有其他异步任务，可以中止
    });

    try {
        initializeFirebaseAdmin();

        const db = admin.firestore();
        const collectionRef = db.collection("activation_codes");

        // 获取文档列表
        let query = collectionRef.limit(10);
        const snapshot = await query.get();

        const documents = snapshot.docs.map(doc => ({
            id: doc.id,
            data: doc.data(),
            createTime: doc.createTime?.toDate(),
            updateTime: doc.updateTime?.toDate()
        }));
        // doc.data.codes 是 map 结构
        const isValid = documents.some(doc => {
            if (doc.data.codes) {
                const map = new Map(Object.entries(doc.data.codes));
                return map.has(activationCode) && map.get(activationCode) === true;
            }
            return false;
        });

        if (!isValid) {
            return error(401, "Invalid activation code");
        }

        const model = url.searchParams.get('model') || "gemini-2.5-flash";

        const resp = await fetch(
            `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`,
            {
                signal: controller.signal,
                method: "POST",
                headers: {
                    Accept: "application/json",
                    "Content-Type": "application/json",
                    "x-goog-api-key": env.GEMINI_API_KEY,
                },
                body: JSON.stringify(requestBody),
            }
        );
        const headers = new Headers(resp.headers);
        headers.delete('content-encoding');
        headers.delete('content-length');
        return new Response(resp.body, {
            status: resp.status,
            headers: headers,
        });
    } catch (err: any) {
        if (err.name === "AbortError") {
            // 请求被取消
            console.log("服务端请求已取消");
        }
        return new Response(JSON.stringify({
            success: false,
            error: `Generate error: ${err}`
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}