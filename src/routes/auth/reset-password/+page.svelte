<script lang="ts">
  import { goto } from "$app/navigation";
  import { onMount } from "svelte";
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import PasswordStrength from "$lib/components/auth/PasswordStrength.svelte";
  import { Input } from "$lib/components/ui";
  import { Lock, Eye, EyeOff, CheckCircle } from "@lucide/svelte";

  let password = $state("");
  let confirmPassword = $state("");
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let oobCode = $state("");
  let passwordError = $state("");
  let confirmPasswordError = $state("");

  onMount(() => {
    // 从URL参数获取oobCode
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get("oobCode");

    if (!code) {
      addToast({
        type: "error",
        message: "无效的重置链接",
      });
      goto("/auth/forgot-password");
      return;
    }

    oobCode = code;
  });

  // 表单验证
  function validateForm(): boolean {
    passwordError = "";
    confirmPasswordError = "";

    let isValid = true;

    // 密码验证
    if (!password.trim()) {
      passwordError = "请输入新密码";
      isValid = false;
    } else if (password.length < 8) {
      passwordError = "密码至少需要8个字符";
      isValid = false;
    } else if (!/[a-z]/.test(password)) {
      passwordError = "密码必须包含小写字母";
      isValid = false;
    }

    // 确认密码验证
    if (!confirmPassword.trim()) {
      confirmPasswordError = "请确认新密码";
      isValid = false;
    } else if (password !== confirmPassword) {
      confirmPasswordError = "两次输入的密码不一致";
      isValid = false;
    }

    return isValid;
  }

  // 重置密码
  async function handleResetPassword(event: Event) {
    event.preventDefault();

    if (!validateForm() || isLoading) return;

    isLoading = true;
    try {
      const success = await authService.handlePasswordReset(oobCode, password);

      if (success) {
        addToast({
          type: "success",
          message: "密码重置成功！请使用新密码登录",
        });
        goto("/login");
      } else {
        addToast({
          type: "error",
          message: "密码重置失败，请重试",
        });
      }
    } catch (error: any) {
      addToast({
        type: "error",
        message: error.message || "密码重置出现错误",
      });
    } finally {
      isLoading = false;
    }
  }

  // 切换密码显示
  function togglePasswordVisibility() {
    showPassword = !showPassword;
  }

  function toggleConfirmPasswordVisibility() {
    showConfirmPassword = !showConfirmPassword;
  }
</script>

<svelte:head>
  <title>重置密码 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="设置您的新密码" />
</svelte:head>

<AuthLayout
  title="设置新密码"
  subtitle="请输入您的新密码"
  guard={{ ignoreAllCheck: true }}
>
  {#snippet children()}
    <div class="space-y-6">
      <!-- 锁图标 -->
      <div class="text-center">
        <div
          class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
        >
          <Lock class="w-8 h-8 text-green-600" />
        </div>
      </div>

      <!-- 重置表单 -->
      <form onsubmit={handleResetPassword} class="space-y-4">
        <!-- 新密码输入 -->
        <div class="relative">
          <Input
            bind:value={password}
            type={showPassword ? "text" : "password"}
            label="新密码"
            placeholder="请输入新密码（至少8个字符）"
            error={passwordError}
            required
            fullWidth
            autocomplete="new-password"
          />
          <button
            type="button"
            onclick={togglePasswordVisibility}
            class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
          >
            {#if showPassword}
              <EyeOff class="w-5 h-5" />
            {:else}
              <Eye class="w-5 h-5" />
            {/if}
          </button>
        </div>

        <!-- 密码强度指示器 -->
        <PasswordStrength {password} />

        <!-- 确认密码输入 -->
        <div class="relative">
          <Input
            bind:value={confirmPassword}
            type={showConfirmPassword ? "text" : "password"}
            label="确认新密码"
            placeholder="请再次输入新密码"
            error={confirmPasswordError}
            required
            fullWidth
            autocomplete="new-password"
          />
          <button
            type="button"
            onclick={toggleConfirmPasswordVisibility}
            class="absolute right-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
          >
            {#if showConfirmPassword}
              <EyeOff class="w-5 h-5" />
            {:else}
              <Eye class="w-5 h-5" />
            {/if}
          </button>
        </div>

        <!-- 提交按钮 -->
        <button
          type="submit"
          disabled={isLoading || !password.trim() || !confirmPassword.trim()}
          class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-green-600 to-emerald-600 text-white font-medium rounded-lg hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
        >
          {#if isLoading}
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            重置中...
          {:else}
            <CheckCircle class="w-4 h-4 mr-2" />
            重置密码
          {/if}
        </button>
      </form>

      <!-- 安全提示 -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-800">
          <p class="font-medium mb-2">密码安全建议：</p>
          <ul class="list-disc list-inside space-y-1">
            <li>使用至少8个字符的密码</li>
            <li>包含大小写字母、数字和特殊字符</li>
            <li>避免使用常见的密码或个人信息</li>
            <li>定期更换密码以保证账户安全</li>
          </ul>
        </div>
      </div>
    </div>
  {/snippet}
</AuthLayout>
