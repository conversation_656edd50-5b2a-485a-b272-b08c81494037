<script lang="ts">
  import { goto } from "$app/navigation";
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import { Input } from "$lib/components/ui";
  import { Mail, ArrowLeft, Send, CheckCircle } from "@lucide/svelte";

  let email = $state("");
  let isLoading = $state(false);
  let emailSent = $state(false);
  let error = $state("");

  // 发送重置邮件
  async function handleSendResetEmail(event: Event) {
    event.preventDefault();

    if (!email.trim()) {
      error = "请输入邮箱地址";
      return;
    }

    if (!authService.verifyEmail(email)) {
      error = "请输入有效的邮箱地址";
      return;
    }

    error = "";
    isLoading = true;

    try {
      await authService.sendPasswordResetEmail(email);
      emailSent = true;
      addToast({
        type: "success",
        message: "密码重置邮件已发送，请查收",
      });
    } catch (error: any) {
      error = error.message || "发送重置邮件失败，请稍后重试";
      addToast({
        type: "error",
        message: error,
      });
    } finally {
      isLoading = false;
    }
  }

  // 返回登录页
  function goToLogin() {
    authService.signOut();
  }

  // 重新发送
  function resetForm() {
    emailSent = false;
    email = "";
    error = "";
  }
</script>

<svelte:head>
  <title>忘记密码 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="重置您的密码" />
</svelte:head>

<AuthLayout
  title={emailSent ? "邮件已发送" : "忘记密码"}
  subtitle={emailSent
    ? "请查收您的邮箱"
    : "输入您的邮箱地址，我们将发送重置链接"}
  guard={{ ignoreAllCheck: true }}
>
  {#snippet children()}
    {#if emailSent}
      <!-- 邮件发送成功页面 -->
      <div class="text-center space-y-6">
        <!-- 成功图标 -->
        <div
          class="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center"
        >
          <CheckCircle class="w-8 h-8 text-green-600" />
        </div>

        <!-- 说明文字 -->
        <div class="space-y-3">
          <p class="text-gray-700">密码重置邮件已发送至：</p>
          <p class="font-medium text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
            {email}
          </p>
          <p class="text-sm text-gray-600">
            请查收邮件并点击重置链接设置新密码。如果没有收到邮件，请检查垃圾邮件文件夹。
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="space-y-3">
          <button
            onclick={resetForm}
            class="w-full flex items-center justify-center px-4 py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200"
          >
            <Send class="w-4 h-4 mr-2" />
            重新发送
          </button>

          <button
            onclick={goToLogin}
            class="w-full flex items-center justify-center px-4 py-2.5 text-gray-600 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
          >
            <ArrowLeft class="w-4 h-4 mr-2" />
            返回登录
          </button>
        </div>

        <!-- 提示信息 -->
        <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
          <div class="text-sm text-yellow-800">
            <p class="font-medium mb-1">注意事项：</p>
            <ul class="list-disc list-inside space-y-1 text-left">
              <li>重置链接有效期为1小时</li>
              <li>每天最多可发送5封重置邮件</li>
              <li>如果长时间未收到邮件，请联系客服</li>
            </ul>
          </div>
        </div>
      </div>
    {:else}
      <!-- 邮箱输入表单 -->
      <div class="space-y-6">
        <!-- 邮件图标 -->
        <div class="text-center">
          <div
            class="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <Mail class="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <!-- 表单 -->
        <form onsubmit={handleSendResetEmail} class="space-y-4">
          <div>
            <Input
              bind:value={email}
              type="email"
              label="邮箱地址"
              placeholder="请输入您的注册邮箱"
              {error}
              required
              fullWidth
              autocomplete="email"
            />
          </div>

          <!-- 提交按钮 -->
          <button
            type="submit"
            disabled={isLoading || !email.trim()}
            class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
          >
            {#if isLoading}
              <svg
                class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                ></circle>
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              发送中...
            {:else}
              <Send class="w-4 h-4 mr-2" />
              发送重置邮件
            {/if}
          </button>
        </form>

        <!-- 返回登录按钮 -->
        <button
          onclick={goToLogin}
          class="w-full flex items-center justify-center px-4 py-2.5 text-gray-600 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
        >
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回登录
        </button>

        <!-- 帮助信息 -->
        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
          <div class="text-sm text-blue-800">
            <p class="font-medium mb-2">忘记密码了？</p>
            <p>
              输入您注册时使用的邮箱地址，我们将向您发送一个安全的密码重置链接。
            </p>
          </div>
        </div>
      </div>
    {/if}
  {/snippet}
</AuthLayout>
