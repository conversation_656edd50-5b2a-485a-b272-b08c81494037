<script lang="ts">
  import { goto } from "$app/navigation";
  import { authService } from "$lib/services/auth.service";
  import { addToast } from "$lib/components/toast/toastStore";
  import { userAuth } from "$lib/stores/app.store";
  import AuthLayout from "$lib/components/auth/AuthLayout.svelte";
  import { Input } from "$lib/components/ui";
  import { Key, Sparkles, ArrowLeft, HelpCircle } from "@lucide/svelte";
  import { LogOut } from "lucide-svelte";

  let activationCode = $state("");
  let isLoading = $state(false);
  let error = $state("");

  // 验证激活码
  async function handleActivation(event: Event) {
    event.preventDefault();

    if (!activationCode.trim()) {
      error = "请输入激活码";
      return;
    }

    if (activationCode.trim().length < 6) {
      error = "激活码长度不正确";
      return;
    }

    error = "";
    isLoading = true;

    try {
      const success = await authService.verifyActivationCode(
        activationCode.trim(),
      );

      if (success) {
        addToast({
          type: "success",
          message: "激活成功！欢迎使用蘑菇AI小说平台",
        });
        goto("/");
      } else {
        error = "激活码无效或已过期，请检查后重试";
      }
    } catch (error: any) {
      error = error.message || "激活失败，请稍后重试";
    } finally {
      isLoading = false;
    }
  }

  // 返回上一页
  function goBack() {
    goto("/");
  }

  // 格式化激活码输入（自动添加分隔符）
  function formatActivationCode(value: string) {
    // 移除所有非字母数字字符
    const cleaned = value.replace(/[^A-Za-z0-9]/g, "");

    // 限制长度
    const limited = cleaned.slice(0, 20);

    // 每4个字符添加一个分隔符
    const formatted = limited.replace(/(.{4})/g, "$1-").replace(/-$/, "");

    return formatted;
  }

  // 处理激活码输入
  function handleCodeInput(event: Event) {
    const target = event.target as HTMLInputElement;
    const formatted = formatActivationCode(target.value);
    activationCode = formatted;
    error = "";
  }

  async function handleUserSignOut() {
    try {
      await authService.signOut(false);
    } catch (err: any) {
      addToast({ type: "error", message: "退出登录失败" });
    }
  }
</script>

<svelte:head>
  <title>激活账户 - 蘑菇🍄 AI小说</title>
  <meta name="description" content="输入激活码完成账户激活" />
</svelte:head>

<AuthLayout
  title="激活您的账户"
  subtitle="输入激活码以完成账户设置"
  guard={{ requireActivated: false }}
>
  {#snippet children()}
    <div class="space-y-6">
      <!-- 激活码图标 -->
      <div class="text-center">
        <div
          class="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center"
        >
          <Key class="w-8 h-8 text-purple-600" />
        </div>
      </div>

      <!-- 说明文字 -->
      <div class="text-center space-y-2">
        <p class="text-gray-700">请输入您收到的激活码</p>
        <p class="text-sm text-gray-600">
          激活码为20位字符，包含大小写字母和数字
        </p>
      </div>

      <!-- 激活码输入表单 -->
      <form onsubmit={handleActivation} class="space-y-4">
        <div>
          <Input
            bind:value={activationCode}
            type="text"
            label="激活码"
            placeholder="xxxx-xxxx-xxxx-xxxx-xxxx"
            {error}
            required
            fullWidth
            autocomplete="off"
            oninput={handleCodeInput}
            class="text-center text-lg font-mono tracking-wider"
          />
        </div>

        <!-- 提交按钮 -->
        <button
          type="submit"
          disabled={isLoading || !activationCode.trim()}
          class="w-full flex items-center justify-center px-4 py-2.5 bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg"
        >
          {#if isLoading}
            <svg
              class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            激活中...
          {:else}
            <Sparkles class="w-4 h-4 mr-2" />
            激活账户
          {/if}
        </button>
      </form>

      <!-- 返回按钮 -->
      <button
        onclick={handleUserSignOut}
        class="w-full flex items-center justify-center px-4 py-2.5 text-red-600 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
      >
        <LogOut class="w-4 h-4 mr-2" />
        退出登录
      </button>

      <!-- 返回按钮 -->
      <button
        onclick={goBack}
        class="w-full flex items-center justify-center px-4 py-2.5 text-gray-600 font-medium rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200"
      >
        <ArrowLeft class="w-4 h-4 mr-2" />
        返回首页
      </button>

      <!-- 帮助信息 -->
      <div class="mt-8 p-4 bg-blue-50 rounded-lg">
        <div class="flex items-start">
          <HelpCircle class="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div class="text-sm text-blue-800">
            <p class="font-medium mb-2">如何获取激活码？</p>
            <ul class="list-disc list-inside space-y-1">
              <li>激活码由平台管理员分发</li>
              <li>请联系客服或查看官方公告获取</li>
              <li>每个激活码只能使用一次</li>
              <li>激活码区分大小写，请准确输入</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 用户信息显示 -->
      {#if $userAuth?.email}
        <div class="text-center text-sm text-gray-600">
          当前账户：{$userAuth.email}
        </div>
      {/if}
    </div>
  {/snippet}
</AuthLayout>
